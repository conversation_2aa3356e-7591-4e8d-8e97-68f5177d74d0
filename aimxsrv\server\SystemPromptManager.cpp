/*++

Copyright (c) Microsoft Corporation. All rights reserved.

Module Name:

    SystemPromptManager.cpp

Abstract:

    Implementation file for the System Prompt Manager component that handles system prompt
    generation for LLM inference operations. Incorporates Cline system prompt format
    for standardized MCP tool and server communication. Provides context-aware
    prompt templates for different LLM inference scenarios.

Author:

    <PERSON><PERSON><PERSON> (rizhang) 07/13/2025

--*/

#include "pch.hxx"
#include "SystemPromptManager.h"
#include "AimxCommon.h"
#include "McpToolManager.h"
#include "StringUtils.h"

//
// SystemPromptManager Implementation
//

HRESULT
SystemPromptManager::Initialize(
    _In_ const std::vector<MCP_SERVER_INFO>& availableServers
    )
/*++

Routine Description:
    Initialize the system prompt manager with available MCP servers.

Arguments:
    availableServers - List of available MCP servers

Return Value:
    S_OK on success, or an error HRESULT on failure.

--*/
{
    std::unique_lock<std::shared_mutex> lock(m_toolsMutex);

    m_availableServers = availableServers;

    // Get tools from all servers
    m_availableTools.clear();
    for (const auto& server : availableServers)
    {
        if (server.isEnabled)
        {
            // Add tools from this server
            for (const auto& tool : server.availableTools)
            {
                m_availableTools.push_back(tool);
            }
        }
    }

    return S_OK;
}

HRESULT
SystemPromptManager::UpdateAvailableTools(
    _In_ const std::vector<MCP_TOOL_INFO>& tools
    )
/*++

Routine Description:
    Update the available tools list for prompt generation.

Arguments:
    tools - Updated list of available tools

Return Value:
    S_OK on success, or an error HRESULT on failure.

--*/
{
    std::unique_lock<std::shared_mutex> lock(m_toolsMutex);
    m_availableTools = tools;
    return S_OK;
}

HRESULT
SystemPromptManager::GetSystemPrompt(
    _In_ SYSTEM_PROMPT_TYPE promptType,
    _In_ const nlohmann::json& context,
    _Out_ std::wstring& systemPrompt
    )
/*++

Routine Description:
    Get system prompt for specific type incorporating Cline format.

Arguments:
    promptType - Type of system prompt needed
    context - Context information for prompt generation
    systemPrompt - Output system prompt

Return Value:
    S_OK on success, or an error HRESULT on failure.

--*/
{
    try
    {
        std::wstring basePrompt = GetBaseSystemPrompt();
        std::wstring specificPrompt;

        switch (promptType)
        {
            case SYSTEM_PROMPT_TYPE::TOOL_ANALYSIS:
                specificPrompt = GetToolAnalysisPrompt();
                break;

            case SYSTEM_PROMPT_TYPE::RESPONSE_GENERATION:
                specificPrompt = GetResponseGenerationPrompt();
                break;

            case SYSTEM_PROMPT_TYPE::PARAMETER_REFINEMENT:
                specificPrompt = GetParameterRefinementPrompt();
                break;

            case SYSTEM_PROMPT_TYPE::RISK_ASSESSMENT:
                specificPrompt = GetRiskAssessmentPrompt();
                break;

            case SYSTEM_PROMPT_TYPE::POWERSHELL_AD_ASSISTANT:
                specificPrompt = GetPowerShellADAssistantPrompt();
                break;

            default:
                return E_INVALIDARG;
        }

        // Build contextual prompt
        std::wstring contextualPrompt = BuildContextualPrompt(promptType, context);

        // Combine all parts
        systemPrompt = basePrompt + L"\n\n" + specificPrompt + L"\n\n" + contextualPrompt;

        return S_OK;
    }
    catch (...)
    {
        return E_FAIL;
    }
}

std::wstring
SystemPromptManager::GetBaseSystemPrompt()
/*++

Routine Description:
    Get the base system prompt incorporating Cline-style format for MCP communication.

Arguments:
    None.

Return Value:
    Base system prompt string.

--*/
{
    return LR"(You are AIMX Assistant, an AI assistant specialized in Windows system administration and Active Directory management. You have access to various MCP (Model Context Protocol) servers and tools that can help you accomplish tasks.

## Core Capabilities

You can help with:
- Active Directory management and queries
- Windows system administration
- PowerShell script execution
- System monitoring and diagnostics
- User and group management
- Security policy configuration

## MCP Tool Usage Guidelines

When using MCP tools, follow these principles:

1. **Tool Selection**: Choose the most appropriate tool for each task
2. **Parameter Validation**: Ensure all required parameters are provided and valid
3. **Error Handling**: Handle tool execution errors gracefully
4. **Security**: Always consider security implications of actions
5. **Confirmation**: Ask for confirmation before making significant changes

## Response Format

Structure your responses as follows:
- **Analysis**: Brief analysis of the user's request
- **Approach**: Explain your planned approach
- **Actions**: List the tools/actions you'll use
- **Results**: Present results clearly
- **Follow-up**: Suggest next steps if applicable

## Safety and Security

- Always verify permissions before making changes
- Warn about potentially destructive operations
- Suggest testing in non-production environments when appropriate
- Follow principle of least privilege)";
}

std::wstring
SystemPromptManager::GetToolAnalysisPrompt()
/*++

Routine Description:
    Get the tool analysis system prompt for determining required tools.

Arguments:
    None.

Return Value:
    Tool analysis system prompt string.

--*/
{
    std::shared_lock<std::shared_mutex> lock(m_toolsMutex);

    std::wstring toolDefinitions;
    GetFormattedToolDefinitions(toolDefinitions);

    std::wstring prompt = L"## Tool Analysis Task\n\n";
    prompt += L"Your task is to analyze the user's request and determine which MCP tools are needed to fulfill it.\n\n";
    prompt += L"### Available Tools:\n\n";
    prompt += toolDefinitions;
    prompt += L"\n\n### Analysis Requirements:\n\n";
    prompt += L"1. **Tool Selection**: Identify all tools needed to complete the request\n";
    prompt += L"2. **Parameter Extraction**: Extract parameters ONLY from what the user explicitly provided\n";
    prompt += L"   - **CRITICAL**: Do NOT make up, construct, or infer parameter values that weren't specified\n";
    prompt += L"   - **CRITICAL**: Do NOT construct Distinguished Names (DN) like \"CN=User,OU=Users,DC=domain,DC=com\" unless the user provided the full DN\n";
    prompt += L"   - **CRITICAL**: Do NOT include parameters with placeholder values like \"Not specified\", \"null\", or empty strings\n";
    prompt += L"   - **CRITICAL**: OMIT parameters entirely if the user didn't specify them - do not include them in the parameters object\n";
    prompt += L"   - **CRITICAL**: Simply omit the parameter/switch completely - PowerShell will use defaults for unspecified optional parameters\n";
    prompt += L"   - **CRITICAL**: Do NOT use example values from documentation or previous queries - only use values explicitly provided by the user\n";
    prompt += L"   - For user-provided names, use them exactly as specified without modification\n";
    prompt += L"   - Only include parameters that are explicitly mentioned or clearly derivable from the user's request\n";
    prompt += L"3. **Dependency Analysis**: Determine the order of tool execution\n";
    prompt += L"4. **Risk Assessment**: Evaluate potential risks of the proposed actions\n";
    prompt += L"5. **Confidence Scoring**: Provide confidence scores for your selections\n";
    prompt += L"6. **PowerShell Command Generation**: For PowerShellDirect tools, generate complete, ready-to-execute commands\n\n";

    prompt += L"### Special Instructions for PowerShell Commands:\n\n";
    prompt += L"When selecting PowerShellDirect tools:\n";
    prompt += L"- Use the full command context (syntax, parameters, examples) provided in the tool definition\n";
    prompt += L"- Generate a PowerShell command using ONLY parameters explicitly provided by the user\n";
    prompt += L"- **CRITICAL**: Do NOT add parameters that the user did not specify, even if they appear in examples\n";
    prompt += L"- **CRITICAL**: Do NOT use example values from the tool documentation as actual parameter values\n";
    prompt += L"- Include the generated command in the \"llm_generated_command\" parameter\n";
    prompt += L"- If the user's request lacks required parameters, generate the command with only what was provided\n";
    prompt += L"- Ensure proper parameter formatting, quoting, and syntax for the parameters that ARE specified\n";
    prompt += L"- The generated command should use only user-provided values, not example or placeholder values\n\n";

    prompt += L"### Response Format:\n\n";
    prompt += L"**IMPORTANT**: For each tool, use the exact \"Server Name\" and \"Tool Name\" values shown above, NOT the combined identifier.\n\n";
    prompt += L"Respond with a JSON object containing the required_tools array with server_name, tool_name, parameters (including llm_generated_command for PowerShellDirect tools), confidence, reasoning, and is_required fields.\n\n";

    prompt += L"Use confidence values from 1 to 100 (integers only, no decimals).\n\n";
    prompt += L"Focus on accuracy and safety in your tool selection.\n";

    return prompt;
}

std::wstring
SystemPromptManager::GetResponseGenerationPrompt()
/*++

Routine Description:
    Get the response generation system prompt for creating final responses.

Arguments:
    None.

Return Value:
    Response generation system prompt string.

--*/
{
    return LR"(## Response Generation Task

You are an expert system administrator assistant. Your task is to generate precise, helpful responses based on tool execution results.

### Core Principles:

1. **Accuracy First**: Present tool results exactly as provided. Never add, modify, or omit information.
2. **No Fabrication**: NEVER create, invent, or hallucinate data that is not present in the tool results. If no data is available, explicitly state that no information was found.
3. **User Intent Recognition**: Distinguish between information requests and action requests.
4. **Complete Information**: For data requests, include ALL details from tool results.
5. **Clear Communication**: Structure responses for easy understanding.

### Response Strategy by Query Type:

**Information Requests** (user wants to see/get/retrieve data):
- Present ALL detailed information from tool results
- Use clear formatting with headers and bullet points
- Include every field and value provided by tools
- Do NOT summarize or condense the data

**Action Requests** (user wants something done):
- Summarize what was accomplished
- Highlight key outcomes and any changes made
- Include relevant details about the operation

**Error/Failure Cases**:
- Clearly state what went wrong
- Include error details from tool results
- Suggest next steps if appropriate

### Response Format:

Respond with a JSON object containing:
```json
{
  "response": "string - main response content with all relevant information",
  "reasoning": "string - brief explanation of how you interpreted the query and results",
  "is_complete": boolean - true if query is fully answered, false if more info needed,
  "follow_up_suggestions": ["string"] - optional suggestions for next steps,
  "additional_questions": ["string"] - optional clarifying questions if needed
}
```

### Critical Requirements:

- The "response" field must contain the complete answer to the user's query
- For information requests, include ALL data from tool results in the response field
- Use clear formatting with headers, bullet points, and proper structure
- Be precise and factual - only use information from the provided tool results
- CRITICAL: Do NOT fabricate, invent, or create any data. If tool results are empty or insufficient, state that clearly instead of making up information

Focus on being helpful, accurate, and complete.)";
}

std::wstring
SystemPromptManager::GetParameterRefinementPrompt()
/*++

Routine Description:
    Get the parameter refinement system prompt for validating tool parameters.

Arguments:
    None.

Return Value:
    Parameter refinement system prompt string.

--*/
{
    return LR"(## Parameter Refinement Task

Your task is to validate and refine parameters for MCP tool execution based on the user's query and tool requirements.

### Guidelines:

1. **Validation**: Ensure all required parameters are present and valid
2. **No Fabrication**: Do NOT make up or construct parameter values that weren't explicitly provided
3. **Safety**: Validate parameters for safety and security
4. **Optimization**: Optimize parameters for best results only when user-provided values exist
5. **Clarity**: Explain any changes or assumptions made

### Critical Rules:
- **NEVER** construct Distinguished Names (DN) unless user provided the full DN
- **NEVER** make up domain structures like "DC=example,DC=com"
- **NEVER** include parameters with placeholder values like "Not specified", "null", or empty strings
- **ALWAYS** omit parameters entirely if they weren't specified by the user
- **ALWAYS** simply omit the parameter/switch completely - PowerShell will use defaults for unspecified optional parameters
- Use simple names exactly as provided by the user without constructing Distinguished Names
- Only refine parameters that were explicitly mentioned by the user

### Parameter Analysis:

- Check parameter types and formats
- Validate against tool schema requirements
- Consider security implications
- Ensure parameters align with user intent
- Suggest improvements or alternatives

### Response Format:

Respond with the refined parameters as a JSON object, maintaining the original structure but with validated/improved values.

Focus on accuracy and safety in parameter refinement.)";
}

std::wstring
SystemPromptManager::GetRiskAssessmentPrompt()
/*++

Routine Description:
    Get the risk assessment system prompt for evaluating action risks.

Arguments:
    None.

Return Value:
    Risk assessment system prompt string.

--*/
{
    return LR"(## Risk Assessment Task

Your task is to assess the risk level of proposed actions and provide detailed risk analysis.

### Risk Categories:

1. **VERY_LOW**: Read-only operations, information queries
2. **LOW**: Minor configuration changes, non-critical updates
3. **MEDIUM**: User/group modifications, policy changes
4. **HIGH**: System configuration changes, service modifications
5. **VERY_HIGH**: Domain-wide changes, security policy modifications
6. **CRITICAL**: Operations that could cause system outages or data loss

### Assessment Criteria:

- **Scope**: How many systems/users are affected
- **Reversibility**: How easily can changes be undone
- **Impact**: Potential business impact of failure
- **Dependencies**: What other systems depend on the target
- **Security**: Security implications of the changes

### Response Format:

Respond with a JSON object containing:
```json
{
  "risk_level": "VERY_LOW|LOW|MEDIUM|HIGH|VERY_HIGH|CRITICAL",
  "risk_assessment": "detailed explanation of risks",
  "mitigation_strategies": ["string"],
  "requires_confirmation": boolean,
  "recommended_precautions": ["string"]
}
```

Focus on thorough risk analysis and practical mitigation strategies.)";
}

HRESULT
SystemPromptManager::GetResponseGenerationUserPrompt(
    _In_ const std::wstring& originalQuery,
    _In_ const std::wstring& toolResults,
    _Out_ std::wstring& userPrompt
    )
/*++

Routine Description:
    Generate user prompt for response generation with clear instructions.

Arguments:
    originalQuery - The original user query
    toolResults - Results from tool execution
    userPrompt - Output user prompt

Return Value:
    S_OK on success, or an error HRESULT on failure.

--*/
{
    try
    {
        userPrompt = L"## Task\n\n";
        userPrompt += L"Generate a comprehensive response to the user's query based on the tool execution results.\n\n";

        userPrompt += L"## Original User Query\n\n";
        userPrompt += originalQuery + L"\n\n";

        userPrompt += L"## Tool Execution Results\n\n";
        userPrompt += toolResults + L"\n\n";

        userPrompt += L"## Instructions\n\n";
        userPrompt += L"1. **For Information Requests**: If the user asked for specific information (user details, system info, data lookup), ";
        userPrompt += L"present ALL the detailed information from the tool results. Do not summarize or omit any data.\n\n";

        userPrompt += L"2. **For Action Requests**: If the user requested an action to be performed, ";
        userPrompt += L"summarize what was accomplished and highlight key outcomes.\n\n";

        userPrompt += L"3. **Structure**: Organize your response clearly with appropriate headings and formatting.\n\n";

        userPrompt += L"4. **Accuracy**: Present tool results exactly as provided. Do not add information not present in the results.\n\n";

        userPrompt += L"5. **No Fabrication**: NEVER invent, create, or hallucinate data. If tool results are empty, incomplete, or missing, explicitly state that no information was found rather than making up example data.\n\n";

        userPrompt += L"6. **Completeness**: Ensure your response fully addresses the user's original query.\n\n";

        userPrompt += L"Provide your response in the required JSON format.";

        return S_OK;
    }
    catch (...)
    {
        return E_FAIL;
    }
}

HRESULT
SystemPromptManager::GetFormattedToolDefinitions(
    _Out_ std::wstring& toolDefinitions
    )
/*++

Routine Description:
    Get formatted tool definitions for inclusion in system prompts.

Arguments:
    toolDefinitions - Output formatted tool definitions

Return Value:
    S_OK on success, or an error HRESULT on failure.

--*/
{
    std::shared_lock<std::shared_mutex> lock(m_toolsMutex);

    toolDefinitions.clear();

    if (m_availableTools.empty())
    {
        toolDefinitions = L"No tools currently available.";
        return S_OK;
    }

    toolDefinitions = FormatToolsForLLM(m_availableTools);
    return S_OK;
}

std::wstring
SystemPromptManager::FormatToolsForLLM(
    _In_ const std::vector<MCP_TOOL_INFO>& tools
    )
/*++

Routine Description:
    Format tools for LLM consumption in Cline-style format.
    Enhanced to provide rich context for PowerShell commands.

Arguments:
    tools - List of tools to format

Return Value:
    Formatted tools string.

--*/
{
    std::wstring formatted;

    for (const auto& tool : tools)
    {
        // Use consistent tool identification format
        std::wstring toolId = McpToolManager::CreateToolId(tool.serverName, tool.toolName);
        formatted += L"### ";
        formatted += toolId;
        formatted += L"\n";
        formatted += L"**Server Name**: ";
        formatted += tool.serverName;
        formatted += L"\n";
        formatted += L"**Tool Name**: ";
        formatted += tool.toolName;
        formatted += L"\n";

        // Check if this is a PowerShell command with rich context
        if (tool.serverName == L"PowerShellDirect" && !tool.inputSchema.empty())
        {
            try
            {
                // Extract rich context from the JSON we stored
                const nlohmann::json& contextJson = tool.inputSchema;

                formatted += L"**Command Type**: PowerShell Command\n";

                // Add parameter and example counts
                if (contextJson.contains("parameterCount") && contextJson.contains("exampleCount"))
                {
                    formatted += L"**Parameters Available**: ";
                    formatted += std::to_wstring(contextJson["parameterCount"].get<int>());
                    formatted += L" | **Examples Available**: ";
                    formatted += std::to_wstring(contextJson["exampleCount"].get<int>());
                    formatted += L"\n";
                }

                // Add key parameters
                if (contextJson.contains("parameterNames") && contextJson["parameterNames"].is_string())
                {
                    std::string paramNames = contextJson["parameterNames"].get<std::string>();
                    if (!paramNames.empty())
                    {
                        formatted += L"**Key Parameters**: ";
                        formatted += Utf8ToWide(paramNames);
                        formatted += L"\n";
                    }
                }

                // Add relevance score
                if (contextJson.contains("score"))
                {
                    formatted += L"**Relevance Score**: ";
                    formatted += std::to_wstring(contextJson["score"].get<double>());
                    formatted += L"\n";
                }

                formatted += L"**Description**: PowerShell Command with Full Context and Examples\n";

                // Add the full command context (this is the key part!)
                if (contextJson.contains("fullText") && contextJson["fullText"].is_string())
                {
                    std::string fullText = contextJson["fullText"].get<std::string>();
                    if (!fullText.empty())
                    {
                        // Truncate if extremely long, but keep generous amount for LLM
                        if (fullText.length() > 4000)
                        {
                            fullText = fullText.substr(0, 4000) + "\n... [truncated - additional examples available]";
                        }
                        formatted += L"**Full Command Context with Examples**:\n";
                        formatted += Utf8ToWide(fullText);
                        formatted += L"\n\n";
                    }
                }

                formatted += L"**IMPORTANT**: This PowerShell command includes complete syntax, parameter descriptions, and usage examples above. ";
                formatted += L"Use this context to generate accurate, properly formatted PowerShell commands.\n";
            }
            catch (...)
            {
                // Fallback to standard formatting if JSON parsing fails
                formatted += L"**Description**: ";
                formatted += tool.description;
                formatted += L"\n";
                if (!tool.inputSchema.empty())
                {
                    formatted += L"**Context**: \n";
                    formatted += Utf8ToWide(tool.inputSchema.dump(2));
                    formatted += L"\n";
                }
            }
        }
        else
        {
            // Standard MCP tool formatting
            formatted += L"**Description**: ";
            formatted += tool.description;
            formatted += L"\n";

            if (!tool.inputSchema.empty())
            {
                formatted += L"**Parameters**: \n";
                formatted += Utf8ToWide(tool.inputSchema.dump(2));
                formatted += L"\n";
            }
        }

        formatted += L"**Available**: ";
        formatted += (tool.isAvailable ? L"Yes" : L"No");
        formatted += L"\n\n";
    }

    return formatted;
}

std::wstring
SystemPromptManager::BuildContextualPrompt(
    _In_ SYSTEM_PROMPT_TYPE /* promptType */,
    _In_ const nlohmann::json& context
    )
/*++

Routine Description:
    Build contextual prompt based on type and context information.

Arguments:
    promptType - Type of prompt being built
    context - Context information

Return Value:
    Contextual prompt string.

--*/
{
    std::wstring contextualPrompt = L"### Current Context:\n\n";

    // Add context-specific information
    if (context.contains("user_query"))
    {
        contextualPrompt += L"**User Query**: ";
        contextualPrompt += Utf8ToWide(context["user_query"].get<std::string>());
        contextualPrompt += L"\n";
    }

    if (context.contains("available_tools_count"))
    {
        contextualPrompt += L"**Available Tools**: ";
        contextualPrompt += std::to_wstring(context["available_tools_count"].get<int>());
        contextualPrompt += L"\n";
    }

    if (context.contains("analysis_type"))
    {
        contextualPrompt += L"**Analysis Type**: ";
        contextualPrompt += Utf8ToWide(context["analysis_type"].get<std::string>());
        contextualPrompt += L"\n";
    }

    contextualPrompt += L"\n";

    return contextualPrompt;
}

std::wstring
SystemPromptManager::GetPowerShellADAssistantPrompt()
/*++

Routine Description:
    Get the PowerShell Active Directory assistant system prompt for generating
    accurate PowerShell commands using RAG-optimized JSON data.

Arguments:
    None.

Return Value:
    PowerShell AD assistant system prompt string.

--*/
{
    return LR"(You are an expert PowerShell for Active Directory assistant. Your sole purpose is to take a user's natural language request and the JSON data for a relevant PowerShell command, and then generate a single, complete, and executable PowerShell command.

**CONTEXT:**
You will be provided with two pieces of information:
1.  **User Request:** A natural language query from a user describing a task they want to perform in Active Directory.
2.  **Command Data:** A JSON object containing detailed information about a single, relevant PowerShell command retrieved from a knowledge base. This JSON includes the command's name, description, parameters, and usage examples.

**YOUR TASK:**
Analyze the user's request and the provided Command Data to construct the most appropriate PowerShell command.

**RULES:**
1.  **SYNTHESIZE, DO NOT INVENT:** Your response MUST be based *only* on the information in the provided JSON Command Data. Do not use any external knowledge.
2.  **IDENTIFY THE CORE COMMAND:** Use the `command_name` from the JSON as the base of your command.
3.  **INFER PARAMETERS AND VALUES:**
    *   Carefully read the user's request to extract values (like names, locations, properties, etc.).
    *   Match these values to the correct parameters described in the `parameters` section of the JSON.
    *   Refer to the `examples` in the JSON to understand common syntax, especially for the `-Filter` parameter.
    *   For `Get-AD*` commands where the user asks for "information", "details", or "properties", always include `-Properties *` to retrieve all available attributes.
4.  **HANDLE MISSING INFORMATION:**
    *   If the user's request is missing critical information required for a parameter (e.g., they ask to move an object but don't specify the destination), use a clear placeholder in angle brackets (e.g., `<Enter-Target-OU-Path-Here>`).
    *   After the command, add a single-line PowerShell comment (`#`) explaining what information the user needs to provide for the placeholder.
5.  **PIPELINING:** If the user's request implies a two-step process (e.g., "find all disabled users and then delete them"), you MAY construct a pipeline using the provided command and other standard commands if the examples suggest it (e.g., `Get-ADUser -Filter 'Enabled -eq $false' | Remove-ADUser`).
6.  **OUTPUT FORMAT:**
    *   Your primary output must be a single PowerShell code block.
    *   Following the code block, provide a concise, one-sentence explanation of what the generated command does.

**EXAMPLE SCENARIO:**

**USER REQUEST:**
"I need to find all the details for the user with the username 'jdoe'."

**COMMAND DATA (abbreviated):**
```json
{
  "command_name": "Get-ADUser",
  "parameters": [
    {"name": "Identity", "description": "Specifies an AD user..."},
    {"name": "Filter", "description": "Specifies a query string..."},
    {"name": "Properties", "description": "Specifies properties to retrieve..."}
  ],
  "examples": [
    {"code": "Get-ADUser -Filter 'Name -like \"*SvcAccount\"'"}
  ]
}
```

**EXPECTED OUTPUT:**
```powershell
Get-ADUser -Identity "jdoe" -Properties *
```
This command retrieves all available properties for the user with the username 'jdoe'.

**ENHANCED RAG DATA UTILIZATION:**
The JSON Command Data now includes enhanced fields from our RAG-optimized format:
- `rag_document`: Contains comprehensive command context, common tasks, and usage scenarios
- `keywords`: Relevant terms and categories for better command understanding
- `required_parameters` and `optional_parameters`: Clear parameter categorization
- `category`: Command categorization (User Management, Group Management, etc.)
- `primary_purpose`: Clear description of what the command does
- Enhanced `examples` with scenario categorization and detailed descriptions

Use all available information from these enhanced fields to generate the most accurate and appropriate PowerShell command for the user's request.

**CRITICAL REQUIREMENTS:**
- Generate ONLY executable PowerShell commands based on the provided JSON data
- Use proper PowerShell syntax and parameter formatting
- Include necessary parameters based on user intent and JSON guidance
- Provide clear explanations for any placeholders used
- Leverage the rich context from the RAG-optimized JSON structure)";
}

HRESULT
SystemPromptManager::GetPowerShellADAssistantUserPrompt(
    _In_ const std::wstring& userRequest,
    _In_ const std::wstring& commandJsonData,
    _Out_ std::wstring& userPrompt
    )
/*++

Routine Description:
    Generate user prompt for PowerShell AD Assistant with RAG-optimized command data.

Arguments:
    userRequest - The user's natural language request
    commandJsonData - RAG-optimized JSON data for the relevant PowerShell command
    userPrompt - Output user prompt

Return Value:
    S_OK on success, or an error HRESULT on failure.

--*/
{
    try
    {
        userPrompt = L"## PowerShell Active Directory Command Generation Task\n\n";

        userPrompt += L"Generate a complete, executable PowerShell command based on the user's request and the provided command data.\n\n";

        userPrompt += L"## User Request\n\n";
        userPrompt += userRequest + L"\n\n";

        userPrompt += L"## Command Data (RAG-Optimized JSON)\n\n";
        userPrompt += L"```json\n";
        userPrompt += commandJsonData + L"\n";
        userPrompt += L"```\n\n";

        userPrompt += L"## Instructions\n\n";
        userPrompt += L"1. **Analyze the User Request**: Extract specific values, names, filters, and requirements from the user's natural language query.\n\n";

        userPrompt += L"2. **Use the Command Data**: Leverage ALL available information from the JSON:\n";
        userPrompt += L"   - `command_name`: The base PowerShell command to use\n";
        userPrompt += L"   - `rag_document`: Comprehensive context about the command's purpose and usage\n";
        userPrompt += L"   - `parameters`: Detailed parameter descriptions and requirements\n";
        userPrompt += L"   - `examples`: Real usage examples with proper syntax\n";
        userPrompt += L"   - `required_parameters` and `optional_parameters`: Parameter categorization\n";
        userPrompt += L"   - `keywords` and `category`: Additional context for command understanding\n\n";

        userPrompt += L"3. **Generate the Command**: Create a single, complete PowerShell command that:\n";
        userPrompt += L"   - Uses the exact `command_name` from the JSON\n";
        userPrompt += L"   - Includes appropriate parameters based on the user's request\n";
        userPrompt += L"   - Follows the syntax patterns shown in the `examples`\n";
        userPrompt += L"   - Uses proper PowerShell formatting and quoting\n\n";

        userPrompt += L"4. **Handle Missing Information**: If the user's request lacks required details, use clear placeholders in angle brackets (e.g., `<Enter-OU-Path>`) and add explanatory comments.\n\n";

        userPrompt += L"5. **Output Format**: Provide your response as:\n";
        userPrompt += L"   - A PowerShell code block with the generated command\n";
        userPrompt += L"   - A brief explanation of what the command does\n\n";

        userPrompt += L"**Remember**: Base your response ONLY on the information provided in the JSON Command Data. Do not use external knowledge or make assumptions beyond what's explicitly provided.";

        return S_OK;
    }
    catch (...)
    {
        return E_FAIL;
    }
}
