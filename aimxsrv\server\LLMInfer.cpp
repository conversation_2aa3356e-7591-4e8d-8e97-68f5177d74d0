/*++

Copyright (c) Microsoft Corporation. All rights reserved.

Module Name:

    LLMInfer.cpp

Abstract:

    Implementation of the LLM Inference component that handles all LLM inference operations.

Author:

    <PERSON><PERSON><PERSON> (r<PERSON><PERSON>) 07/11/2025

--*/

#include "pch.hxx"
#include "LLMInfer.h"
#include "StringUtils.h"
#include "AimxConstants.h"
#include "McpToolManager.h"
#include "ConversationManager.h"
#include "Debug.h"
#include "LLMInfer.cpp.tmh"

// cpprestsdk includes
#include <cpprest/http_client.h>
#include <cpprest/json.h>
#include <chrono>

using namespace web;
using namespace web::http;
using namespace web::http::client;

// Static member definitions
LLMInfer* LLMInfer::s_instance = nullptr;
std::mutex LLMInfer::s_instanceMutex;

HRESULT
LLMInfer::Initialize()
/*++

Routine Description:
    Initialize the LLM Inference component using global AimxLlmConfig.

Arguments:
    None.

Return Value:
    S_OK on success, or an error HRESULT on failure.

--*/
{
    std::lock_guard<std::mutex> lock(s_instanceMutex);

    TraceInfo(AimxLlmInfer, "Initializing LLM Inference component");

    if (s_instance != nullptr)
    {
        TraceInfo(AimxLlmInfer, "LLM Inference component already initialized");
        return AIMX_S_COMPONENT_ALREADY_INITIALIZED;
    }

    s_instance = new (std::nothrow) LLMInfer();
    if (s_instance == nullptr)
    {
        TraceErr(AimxLlmInfer, "Failed to allocate LLM Inference instance");
        return E_OUTOFMEMORY;
    }

    HRESULT hr = s_instance->InitializeInternal();
    if (FAILED(hr))
    {
        TraceErr(AimxLlmInfer, "Failed to initialize LLM Inference internal state: %!HRESULT!", hr);
        delete s_instance;
        s_instance = nullptr;
        return hr;
    }

    s_instance->m_initialized = true;

    TraceInfo(AimxLlmInfer, "LLM Inference component initialized successfully");
    return S_OK;
}

HRESULT
LLMInfer::InitializeInternal()
/*++

Routine Description:
    Internal initialization method that creates LLM client and system prompt manager
    using the global AimxLlmConfig singleton.

Arguments:
    None.

Return Value:
    S_OK on success, or an error HRESULT on failure.

--*/
{
    TraceInfo(AimxLlmInfer, "Initializing LLM client and system prompt manager");

    try
    {
        // Initialize LLM configuration from registry first
        TraceInfo(AimxLlmInfer, "Initializing LLM configuration from registry");
        AimxLlmConfig& llmConfig = AimxLlmConfig::Instance();

        // Verify that configuration was loaded successfully
        std::wstring endpointUrl = llmConfig.GetEndpointUrl();
        std::wstring model = llmConfig.GetModel();

        if (endpointUrl.empty())
        {
            TraceErr(AimxLlmInfer, "LLM endpoint URL is empty after configuration initialization - check registry settings");
            TraceErr(AimxLlmInfer, "Required registry key: HKLM\\%ws\\%ws",
                    AimxConstants::Registry::AIMX_REGISTRY_PARAMETERS,
                    AimxConstants::Registry::AIMX_REGISTRY_KEYNAME_FOUNDRYLOCALPORT);
            return E_FAIL;
        }

        if (model.empty())
        {
            TraceWarn(AimxLlmInfer, "LLM model is empty after configuration initialization - check registry settings");
            TraceWarn(AimxLlmInfer, "Optional registry key: HKLM\\%ws\\%ws",
                    AimxConstants::Registry::AIMX_REGISTRY_PARAMETERS,
                    AimxConstants::Registry::AIMX_REGISTRY_KEYNAME_FOUNDRYLOCALMODELID);
            TraceWarn(AimxLlmInfer, "Continuing with empty model - this may cause LLM requests to fail");
        }

        TraceInfo(AimxLlmInfer, "LLM configuration loaded successfully:");
        TraceInfo(AimxLlmInfer, "  Endpoint URL: %ws", endpointUrl.c_str());
        TraceInfo(AimxLlmInfer, "  Model: %ws", model.c_str());
        TraceInfo(AimxLlmInfer, "  Temperature: %f", llmConfig.GetTemperature());
        TraceInfo(AimxLlmInfer, "  TopK: %d", llmConfig.GetTopK());
        TraceInfo(AimxLlmInfer, "  TopP: %f", llmConfig.GetTopP());

        // Create system prompt manager
        m_promptManager = std::make_unique<SystemPromptManager>();
        if (!m_promptManager)
        {
            TraceErr(AimxLlmInfer, "Failed to create system prompt manager");
            return E_FAIL;
        }
        
        // Initialize prompt manager with available servers
        std::vector<MCP_SERVER_INFO> availableServers;
        HRESULT hr = McpSvrMgr::GetEnabledMcpServers(availableServers);
        if (SUCCEEDED(hr))
        {
            hr = m_promptManager->Initialize(availableServers);
            if (FAILED(hr))
            {
                TraceWarn(AimxLlmInfer, "Failed to initialize prompt manager with servers: %!HRESULT!", hr);
                // Continue anyway - can be updated later
            }
        }
        
        TraceInfo(AimxLlmInfer, "LLM client and prompt manager initialized successfully");
        return S_OK;
    }
    catch (...)
    {
        TraceErr(AimxLlmInfer, "Exception during LLM Inference initialization");
        return E_FAIL;
    }
}

void
LLMInfer::Uninitialize()
/*++

Routine Description:
    Uninitialize the LLM Inference component and cleanup resources.

Arguments:
    None.

Return Value:
    None.

--*/
{
    std::lock_guard<std::mutex> lock(s_instanceMutex);
    
    TraceInfo(AimxLlmInfer, "Uninitializing LLM Inference component");
    
    if (s_instance != nullptr)
    {
        // Reset unique pointers to cleanup resources
        s_instance->m_promptManager.reset();

        delete s_instance;
        s_instance = nullptr;
    }
    
    TraceInfo(AimxLlmInfer, "LLM Inference component uninitialized");
}

HRESULT
LLMInfer::AnalyzeUserQuery(
    _In_ const std::wstring& userQuery,
    _In_ const std::vector<MCP_TOOL_INFO>& availableTools,
    _Out_ LLM_ANALYSIS_RESULT& analysisResult,
    _In_opt_ std::shared_ptr<ConversationSession> conversationSession
    )
/*++

Routine Description:
    Analyze user query to determine required tools/mcpservers.
    Uses structured system prompts to guide LLM in tool selection and parameter extraction.

Arguments:
    userQuery - The user's natural language query
    availableTools - List of available MCP tools
    analysisResult - Output analysis result with tool requirements

Return Value:
    S_OK on success, or an error HRESULT on failure.

--*/
{
    if (s_instance == nullptr)
    {
        return AIMX_E_COMPONENT_NOT_INITIALIZED;
    }
    
    TraceInfo(AimxLlmInfer, "Analyzing user query for tool requirements");

    // Send analysis start message to conversation session
    if (conversationSession)
    {
        std::wstring analysisMsg = L"[" + std::wstring(AimxConstants::MessageStages::AIMX_STAGE_LLM_REQUEST) + L"] User wants: \"" + userQuery + L"\". Analyzing available tools to fulfill this request.";
        conversationSession->SendMessage(AIMX_MSG_LLM_INFERENCE, analysisMsg);
    }

    try
    {
        // Update prompt manager with current tools
        HRESULT hr = s_instance->m_promptManager->UpdateAvailableTools(availableTools);
        if (FAILED(hr))
        {
            TraceWarn(AimxLlmInfer, "Failed to update available tools in prompt manager: %!HRESULT!", hr);
        }

        if (conversationSession)
        {
            std::wstring toolsMsg = L"[" + std::wstring(AimxConstants::MessageStages::AIMX_STAGE_LLM_REQUEST) + L"] Found " + std::to_wstring(availableTools.size()) + L" available tools. Preparing LLM analysis...";
            conversationSession->SendMessage(AIMX_MSG_LLM_INFERENCE, toolsMsg);
        }

        // Check if we have PowerShell tools - use PowerShell AD Assistant prompts
        bool hasPowerShellTools = false;
        for (const auto& tool : availableTools)
        {
            if (tool.serverName == L"PowerShellDirect" ||
                (tool.inputSchema.contains("command_name") && tool.inputSchema.contains("rag_document")))
            {
                hasPowerShellTools = true;
                break;
            }
        }

        // Build context for analysis
        nlohmann::json context;
        context["user_query"] = WideToUtf8(userQuery);
        context["available_tools_count"] = availableTools.size();

        // Get appropriate system prompt based on tool types
        std::wstring systemPrompt;
        SYSTEM_PROMPT_TYPE promptType;

        if (hasPowerShellTools)
        {
            TraceInfo(AimxLlmInfer, "Detected PowerShell tools, using PowerShell AD Assistant system prompt");
            context["analysis_type"] = "powershell_command_generation";
            context["rag_optimized"] = true;
            context["powershell_mode"] = true;
            promptType = SYSTEM_PROMPT_TYPE::POWERSHELL_AD_ASSISTANT;
        }
        else
        {
            context["analysis_type"] = "tool_selection";
            promptType = SYSTEM_PROMPT_TYPE::TOOL_ANALYSIS;
        }

        hr = s_instance->m_promptManager->GetSystemPrompt(promptType, context, systemPrompt);
        if (FAILED(hr))
        {
            TraceErr(AimxLlmInfer, "Failed to get system prompt: %!HRESULT!", hr);
            return hr;
        }
        
        // Send request to LLM
        if (conversationSession)
        {
            conversationSession->SendMessage(AIMX_MSG_LLM_INFERENCE, L"[" + std::wstring(AimxConstants::MessageStages::AIMX_STAGE_LLM_REQUEST) + L"] Sending query to LLM service for tool analysis...");
        }

        // Notify that LLM processing is starting for tool analysis
        if (conversationSession)
        {
            conversationSession->UpdateStatus(AimxConstants::MessageStages::AIMX_STAGE_LLM_PROCESSING, L"Analyzing query using AI assistant");
        }

        // Generate appropriate user prompt using SystemPromptManager
        std::wstring userPrompt;
        if (hasPowerShellTools)
        {
            // Use SystemPromptManager to generate PowerShell tool analysis prompt
            hr = s_instance->m_promptManager->GetPowerShellToolAnalysisUserPrompt(
                userQuery,
                availableTools,
                userPrompt
            );

            if (FAILED(hr))
            {
                TraceErr(AimxLlmInfer, "Failed to get PowerShell tool analysis user prompt: %!HRESULT!", hr);
                return hr;
            }

            TraceInfo(AimxLlmInfer, "Generated PowerShell tool analysis user prompt: %Iu characters", userPrompt.length());
        }
        else
        {
            // Use standard user query for non-PowerShell tools
            userPrompt = userQuery;
        }

        std::wstring llmResponse;
        hr = s_instance->SendLlmRequest(systemPrompt, userPrompt, llmResponse);
        if (FAILED(hr))
        {
            TraceErr(AimxLlmInfer, "Failed to send LLM request for tool analysis: %!HRESULT!", hr);
            if (conversationSession)
            {
                conversationSession->SendMessage(AIMX_MSG_ERROR_MESSAGE, L"[" + std::wstring(AimxConstants::MessageStages::AIMX_STAGE_LLM_REQUEST) + L"] Failed to communicate with LLM service");
                conversationSession->UpdateStatus(AimxConstants::MessageStages::AIMX_STAGE_LLM_RESPONSE, L"AI analysis failed");
            }
            return hr;
        }

        // Notify that LLM processing completed for tool analysis
        if (conversationSession)
        {
            conversationSession->UpdateStatus(AimxConstants::MessageStages::AIMX_STAGE_LLM_RESPONSE, L"AI analysis completed successfully");
        }

        if (conversationSession)
        {
            std::wstring responseMsg = L"[" + std::wstring(AimxConstants::MessageStages::AIMX_STAGE_LLM_RESPONSE) + L"] Received LLM response (" + std::to_wstring(llmResponse.length()) + L" characters). Parsing tool requirements...";
            conversationSession->SendMessage(AIMX_MSG_LLM_RESPONSE, responseMsg);
        }

        // Parse LLM response as tool analysis JSON (standard flow for all tools including PowerShell)
        hr = s_instance->ParseToolAnalysisResponse(llmResponse, analysisResult, conversationSession);

        if (FAILED(hr))
        {
            TraceErr(AimxLlmInfer, "Failed to parse LLM response: %!HRESULT!", hr);
            if (conversationSession)
            {
                conversationSession->SendMessage(AIMX_MSG_ERROR_MESSAGE, L"[" + std::wstring(AimxConstants::MessageStages::AIMX_STAGE_LLM_PARSING) + L"] Failed to parse LLM response for tool analysis");
            }
            return hr;
        }
        
        // Validate tool requirements
        hr = s_instance->ValidateToolRequirements(analysisResult.requiredTools, availableTools);
        if (FAILED(hr))
        {
            TraceWarn(AimxLlmInfer, "Tool requirements validation failed: %!HRESULT!", hr);
            // Continue with potentially invalid requirements - let caller decide
        }

        // Send analysis completion message
        if (conversationSession)
        {
            conversationSession->SendMessage(AIMX_MSG_LLM_RESPONSE, L"[" + std::wstring(AimxConstants::MessageStages::AIMX_STAGE_LLM_PARSING) + L"] LLM analysis completed, proceeding to execution planning");
        }

        TraceInfo(AimxLlmInfer, "Successfully analyzed user query, found %d tool requirements",
                 static_cast<int>(analysisResult.requiredTools.size()));

        return S_OK;
    }
    catch (...)
    {
        TraceErr(AimxLlmInfer, "Exception during user query analysis");
        return E_FAIL;
    }
}

HRESULT
LLMInfer::GenerateResponse(
    _In_ const std::wstring& originalQuery,
    _In_ const std::wstring& toolResults,
    _Out_ LLM_RESPONSE_RESULT& responseResult,
    _In_opt_ std::shared_ptr<ConversationSession> conversationSession
    )
/*++

Routine Description:
    Generate response from original query and tool results using Cline-style prompting.

Arguments:
    originalQuery - The original user query
    toolResults - Results from tool execution
    responseResult - Output response result

Return Value:
    S_OK on success, or an error HRESULT on failure.

--*/
{
    if (s_instance == nullptr)
    {
        return AIMX_E_COMPONENT_NOT_INITIALIZED;
    }
    
    TraceInfo(AimxLlmInfer, "Generating response from tool results");

    if (conversationSession)
    {
        std::wstring contextMsg = L"[" + std::wstring(AimxConstants::MessageStages::AIMX_STAGE_LLM_REQUEST) + L"] Generating final response. Original query: \"" + originalQuery + L"\"";
        conversationSession->SendMessage(AIMX_MSG_LLM_INFERENCE, contextMsg);

        if (!toolResults.empty())
        {
            std::wstring toolResultsMsg = L"[" + std::wstring(AimxConstants::MessageStages::AIMX_STAGE_LLM_REQUEST) + L"] Tool results to incorporate: " + toolResults;
            conversationSession->SendMessage(AIMX_MSG_LLM_INFERENCE, toolResultsMsg);
        }
    }

    try
    {
        // Build context for response generation
        nlohmann::json context;
        context["original_query"] = WideToUtf8(originalQuery);
        context["tool_results"] = WideToUtf8(toolResults);
        context["response_type"] = "final_answer";
        
        // Get system prompt for response generation
        std::wstring systemPrompt;
        HRESULT hr = s_instance->m_promptManager->GetSystemPrompt(
            SYSTEM_PROMPT_TYPE::RESPONSE_GENERATION,
            context,
            systemPrompt
        );
        if (FAILED(hr))
        {
            TraceErr(AimxLlmInfer, "Failed to get response generation system prompt: %!HRESULT!", hr);
            return hr;
        }
        
        // Get user prompt from SystemPromptManager
        std::wstring userPrompt;
        hr = s_instance->m_promptManager->GetResponseGenerationUserPrompt(
            originalQuery,
            toolResults,
            userPrompt
        );
        if (FAILED(hr))
        {
            TraceErr(AimxLlmInfer, "Failed to get response generation user prompt: %!HRESULT!", hr);
            return hr;
        }
        
        // Notify that LLM processing is starting
        if (conversationSession)
        {
            std::wstring processingMsg = L"[" + std::wstring(AimxConstants::MessageStages::AIMX_STAGE_LLM_PROCESSING) + L"] Generating final response using AI assistant";
            conversationSession->UpdateStatus(AimxConstants::MessageStages::AIMX_STAGE_LLM_PROCESSING, L"Generating final response using AI assistant");
        }

        // Send request to LLM
        std::wstring llmResponse;
        hr = s_instance->SendLlmRequest(systemPrompt, userPrompt, llmResponse);
        if (FAILED(hr))
        {
            TraceErr(AimxLlmInfer, "Failed to send LLM request for response generation: %!HRESULT!", hr);

            // Notify that LLM processing failed
            if (conversationSession)
            {
                conversationSession->UpdateStatus(AimxConstants::MessageStages::AIMX_STAGE_LLM_RESPONSE, L"AI response generation failed");
            }
            return hr;
        }

        // Notify that LLM processing completed successfully
        if (conversationSession)
        {
            conversationSession->UpdateStatus(AimxConstants::MessageStages::AIMX_STAGE_LLM_RESPONSE, L"AI response generated successfully");
        }
        
        // Parse LLM response
        hr = s_instance->ParseResponseGenerationResult(llmResponse, responseResult);
        if (FAILED(hr))
        {
            TraceErr(AimxLlmInfer, "Failed to parse response generation result: %!HRESULT!", hr);
            return hr;
        }
        
        TraceInfo(AimxLlmInfer, "Successfully generated response");
        return S_OK;
    }
    catch (...)
    {
        TraceErr(AimxLlmInfer, "Exception during response generation");
        return E_FAIL;
    }
}



HRESULT
LLMInfer::TestLlmConnectivity(
    _Out_ std::wstring& testResult
    )
/*++

Routine Description:
    Test LLM connectivity by sending a simple test request.

Arguments:
    testResult - Output test result message

Return Value:
    S_OK on success, or an error HRESULT on failure.

--*/
{
    if (s_instance == nullptr)
    {
        testResult = L"LLM Inference component not initialized";
        return AIMX_E_COMPONENT_NOT_INITIALIZED;
    }

    try
    {
        std::wstring systemPrompt = L"You are a helpful assistant. Respond with exactly 'OK' to confirm connectivity.";
        std::wstring userPrompt = L"Test connectivity";
        std::wstring response;

        HRESULT hr = s_instance->SendLlmRequest(systemPrompt, userPrompt, response);
        if (FAILED(hr))
        {
            testResult = L"LLM connectivity test failed - unable to send request";
            return hr;
        }

        if (response.find(L"OK") != std::wstring::npos)
        {
            testResult = L"LLM connectivity test successful";
            return S_OK;
        }
        else
        {
            testResult = L"LLM connectivity test failed - unexpected response: " + response;
            return E_FAIL;
        }
    }
    catch (...)
    {
        testResult = L"LLM connectivity test failed - exception occurred";
        return E_FAIL;
    }
}

LLMInfer*
LLMInfer::GetInstance()
/*++

Routine Description:
    Get the singleton instance of the LLM Inference component.

Arguments:
    None.

Return Value:
    Pointer to the singleton instance, or nullptr if not initialized.

--*/
{
    std::lock_guard<std::mutex> lock(s_instanceMutex);
    return s_instance;
}

// Private constructor
LLMInfer::LLMInfer()
    : m_initialized(false)
    , m_promptManager(nullptr)
{
    TraceInfo(AimxLlmInfer, "LLM Inference instance created");
}

// Private destructor
LLMInfer::~LLMInfer()
{
    TraceInfo(AimxLlmInfer, "LLM Inference instance destroyed");
}

HRESULT
LLMInfer::SendLlmRequest(
    _In_ const std::wstring& systemPrompt,
    _In_ const std::wstring& userPrompt,
    _Out_ std::wstring& response
    )
/*++

Routine Description:
    Send a request to the LLM with system and user prompts using cpprestsdk.

Arguments:
    systemPrompt - The system prompt to set context
    userPrompt - The user prompt/query
    response - Output LLM response

Return Value:
    S_OK on success, or an error HRESULT on failure.

--*/
{
    try
    {
        TraceInfo(AimxLlmInfer, "Sending HTTP POST request to LLM endpoint");

        // Get configuration from global singleton
        AimxLlmConfig& llmConfig = AimxLlmConfig::Instance();

        // Create JSON payload
        nlohmann::json requestJson;
        requestJson[AimxConstants::JsonFields::AIMX_JSON_KEY_MODEL] = WideToUtf8(llmConfig.GetModel());
        requestJson[AimxConstants::JsonFields::AIMX_JSON_KEY_MESSAGES] = {
            {{AimxConstants::JsonFields::AIMX_JSON_KEY_ROLE, AimxConstants::LlmFoundryRoleType::AIMX_LLM_FOUNDRY_ROLE_TYPE_SYSTEM}, {AimxConstants::JsonFields::AIMX_JSON_KEY_CONTENT, WideToUtf8(systemPrompt)}},
            {{AimxConstants::JsonFields::AIMX_JSON_KEY_ROLE, AimxConstants::LlmFoundryRoleType::AIMX_LLM_FOUNDRY_ROLE_TYPE_USER}, {AimxConstants::JsonFields::AIMX_JSON_KEY_CONTENT, WideToUtf8(userPrompt)}}
        };
        requestJson[AimxConstants::JsonFields::AIMX_JSON_KEY_TEMPERATURE] = llmConfig.GetTemperature();
        requestJson[AimxConstants::JsonFields::AIMX_JSON_KEY_TOP_K] = llmConfig.GetTopK();
        requestJson[AimxConstants::JsonFields::AIMX_JSON_KEY_TOP_P] = llmConfig.GetTopP();
        requestJson[AimxConstants::JsonFields::AIMX_JSON_KEY_STREAM] = false;
        requestJson["max_tokens"] = AimxConstants::LlmInferenceDefaults::LLM_MAX_TOKENS_4K;


        // Convert JSON to string first for logging
        std::string jsonStr = requestJson.dump(4); // Pretty print with 4-space indentation

        // Log the complete JSON payload
        TraceInfo(AimxLlmInfer, "=== FULL LLM REQUEST PAYLOAD START ===");
        TraceInfo(AimxLlmInfer, "JSON payload size: %d bytes", static_cast<int>(jsonStr.size()));
        TraceInfo(AimxLlmInfer, "System prompt length: %d characters", static_cast<int>(systemPrompt.length()));
        TraceInfo(AimxLlmInfer, "User prompt length: %d characters", static_cast<int>(userPrompt.length()));
        TraceInfo(AimxLlmInfer, "System prompt content: %s", WideToUtf8(systemPrompt).c_str());
        TraceInfo(AimxLlmInfer, "User prompt content: %s", WideToUtf8(userPrompt).c_str());
        TraceInfo(AimxLlmInfer, "Complete JSON payload:");
        TraceInfo(AimxLlmInfer, "%s", jsonStr.c_str());
        TraceInfo(AimxLlmInfer, "=== FULL LLM REQUEST PAYLOAD END ===");

        // Convert JSON to wide string for cpprestsdk
        std::wstring jsonData = Utf8ToWide(jsonStr);

        // Use cpprestsdk for HTTP POST, we are abandoning the LLMClientlib.
        // RUPO_TODO: remove the depenendcy from AIMXAPP
        utility::string_t llmEndpointUrl = llmConfig.GetEndpointUrl();

        // Log the endpoint URL for debugging
        if (llmEndpointUrl.empty())
        {
            TraceErr(AimxLlmInfer, "Ensure Foundry Local AI is properly configured and the port is set");
            return E_INVALIDARG;
        }

        // Create HTTP client configuration with timeout settings
        http_client_config config;
        config.set_timeout(std::chrono::seconds(300));

        // Create HTTP client with configuration
        TraceInfo(AimxLlmInfer, "Creating HTTP client for URL: %s", WideToUtf8(llmEndpointUrl).c_str());
        http_client client(llmEndpointUrl, config);

        // Create HTTP request
        http_request request(methods::POST);

        // Set content type header
        request.headers().set_content_type(U("application/json"));

        // Set request body
        request.set_body(jsonData, U("application/json"));

        // Send the request and get response
        TraceInfo(AimxLlmInfer, "Sending HTTP request to: %s with timeout: 30 seconds", WideToUtf8(llmEndpointUrl).c_str());

        auto responseTask = client.request(request);

        // Wait for response with timeout handling
        try
        {
            auto httpResponse = responseTask.get();

            // Check status code
            auto statusCode = httpResponse.status_code();
            TraceInfo(AimxLlmInfer, "HTTP response status code: %d", static_cast<int>(statusCode));

            if (statusCode != status_codes::OK)
            {
                TraceErr(AimxLlmInfer, "HTTP request failed with status code: %d", static_cast<int>(statusCode));

                // Log additional details for debugging
                TraceErr(AimxLlmInfer, "=== HTTP ERROR DETAILS START ===");
                TraceErr(AimxLlmInfer, "Status code: %d", static_cast<int>(statusCode));
                TraceErr(AimxLlmInfer, "Endpoint URL: %s", WideToUtf8(llmEndpointUrl).c_str());

                // Try to get response body for error details
                try
                {
                    auto responseBody = httpResponse.extract_string().get();
                    TraceErr(AimxLlmInfer, "Error response body: %s", WideToUtf8(responseBody).c_str());
                }
                catch (...)
                {
                    TraceErr(AimxLlmInfer, "Could not extract error response body");
                }
                TraceErr(AimxLlmInfer, "=== HTTP ERROR DETAILS END ===");

                return E_FAIL;
            }

            // Extract response body
            TraceVerb(AimxLlmInfer, "Extracting response body");
            auto bodyTask = httpResponse.extract_string();
            auto responseBody = bodyTask.get();

            TraceInfo(AimxLlmInfer, "=== FULL LLM HTTP RESPONSE START ===");
            TraceInfo(AimxLlmInfer, "Raw HTTP response length: %d", static_cast<int>(responseBody.length()));
            TraceInfo(AimxLlmInfer, "Raw HTTP response: %s", WideToUtf8(responseBody).c_str());
            TraceInfo(AimxLlmInfer, "=== FULL LLM HTTP RESPONSE END ===");

            // Extract the actual LLM content from the HTTP response JSON
            HRESULT hr = ExtractLlmContentFromHttpResponse(responseBody, response);
            if (FAILED(hr))
            {
                TraceErr(AimxLlmInfer, "Failed to extract LLM content from HTTP response");
                LOGERROR("Failed to extract LLM content from HTTP response");
                return hr;
            }

            // Log extracted LLM content
            TraceInfo(AimxLlmInfer, "=== EXTRACTED LLM CONTENT START ===");
            TraceInfo(AimxLlmInfer, "Extracted content length: %d characters", static_cast<int>(response.length()));
            TraceInfo(AimxLlmInfer, "Extracted content: %s", WideToUtf8(response).c_str());
            TraceInfo(AimxLlmInfer, "=== EXTRACTED LLM CONTENT END ===");
        }
        catch (const web::http::http_exception& e)
        {
            TraceErr(AimxLlmInfer, "HTTP exception in SendLlmRequest: %s", e.what());
            return E_FAIL;
        }

        return S_OK;
    }
    catch (const std::exception& e)
    {
        TraceErr(AimxLlmInfer, "Exception in SendLlmRequest: %s", e.what());
        return E_FAIL;
    }
    catch (...)
    {
        TraceErr(AimxLlmInfer, "Unknown exception in SendLlmRequest");
        return E_FAIL;
    }
}

HRESULT
LLMInfer::ParseToolAnalysisResponse(
    _In_ const std::wstring& llmResponse,
    _Out_ LLM_ANALYSIS_RESULT& analysisResult,
    _In_opt_ std::shared_ptr<ConversationSession> conversationSession
    )
/*++

Routine Description:
    Parse LLM response for tool analysis into structured result.

Arguments:
    llmResponse - The raw LLM response
    analysisResult - Output parsed analysis result

Return Value:
    S_OK on success, or an error HRESULT on failure.

--*/
{
    try
    {

        // Initialize result
        analysisResult = {};

        // Convert response to UTF-8 for JSON parsing
        std::string utf8Response = WideToUtf8(llmResponse);

        // Sanitize utf8Response if it starts with "```json" (similar to ParseResponseGenerationResult)
        const std::string fence = "```json";
        const std::string fence_end = "```";

        size_t start = utf8Response.find(fence);
        if (start != std::string::npos)
        {
            size_t json_start = utf8Response.find('\n', start);

            // Find the closing fence, but make sure it's after the opening fence
            size_t end = std::string::npos;
            size_t search_pos = start + fence.length();
            while (search_pos < utf8Response.length())
            {
                size_t found = utf8Response.find(fence_end, search_pos);
                if (found != std::string::npos && found != start)
                {
                    end = found;
                    break;
                }
                search_pos = found + 1;
                if (found == std::string::npos) break;
            }

            if (json_start != std::string::npos && end != std::string::npos && end > json_start)
            {
                std::string originalResponse = utf8Response;
                utf8Response = utf8Response.substr(json_start + 1, end - json_start - 1);
                TraceInfo(AimxLlmInfer, "Extracted JSON from markdown code block");
            }
            else
            {
                if (json_start != std::string::npos)
                {
                    utf8Response = utf8Response.substr(json_start + 1);
                }
            }
        }

        // Try to parse as JSON
        nlohmann::json responseJson;
        bool isValidJson = nlohmann::json::accept(utf8Response);

        // If JSON is invalid, try to fix truncated JSON
        if (!isValidJson)
        {
            std::string fixedJson = utf8Response;

            // Check if it looks like truncated JSON (starts with { but doesn't end properly)
            if (fixedJson.find('{') != std::string::npos)
            {
                // Count open and close braces
                int openBraces = 0;
                int closeBraces = 0;
                for (char c : fixedJson)
                {
                    if (c == '{') openBraces++;
                    if (c == '}') closeBraces++;
                }

                // If we have unmatched braces, try to close them
                if (openBraces > closeBraces)
                {
                    // Add missing closing braces
                    for (int i = 0; i < (openBraces - closeBraces); i++)
                    {
                        fixedJson += "}";
                    }

                    // Test if the fixed JSON is valid
                    if (nlohmann::json::accept(fixedJson))
                    {
                        utf8Response = fixedJson;
                        isValidJson = true;
                    }
                }
            }
        }

        if (isValidJson)
        {
            responseJson = nlohmann::json::parse(utf8Response);
            TraceInfo(AimxLlmInfer, "Successfully parsed LLM response as JSON");

            // Extract tool requirements
            if (responseJson.contains("required_tools") && responseJson["required_tools"].is_array())
            {
                size_t toolCount = responseJson["required_tools"].size();
                TraceInfo(AimxLlmInfer, "Found required_tools array with %d tools", static_cast<int>(toolCount));

                for (size_t i = 0; i < toolCount; ++i)
                {
                    const auto& toolJson = responseJson["required_tools"][i];
                    TOOL_REQUIREMENT toolReq = {};

                    if (toolJson.contains("server_name") && toolJson["server_name"].is_string())
                    {
                        toolReq.serverName = Utf8ToWide(toolJson["server_name"].get<std::string>());
                    }

                    if (toolJson.contains("tool_name") && toolJson["tool_name"].is_string())
                    {
                        toolReq.toolName = Utf8ToWide(toolJson["tool_name"].get<std::string>());
                    }

                    if (toolJson.contains("parameters") && toolJson["parameters"].is_object())
                    {
                        toolReq.parameters = toolJson["parameters"];
                    }

                    if (toolJson.contains("confidence") && toolJson["confidence"].is_number())
                    {
                        // Convert from 1-100 scale to 0.01-1.0 scale
                        int confidenceInt = toolJson["confidence"].get<int>();
                        toolReq.confidence = static_cast<float>(confidenceInt) / 100.0f;
                    }

                    if (toolJson.contains("reasoning") && toolJson["reasoning"].is_string())
                    {
                        toolReq.reasoning = Utf8ToWide(toolJson["reasoning"].get<std::string>());
                    }

                    if (toolJson.contains("is_required") && toolJson["is_required"].is_boolean())
                    {
                        toolReq.isRequired = toolJson["is_required"].get<bool>();
                    }

                    analysisResult.requiredTools.push_back(toolReq);
                }
            }
            else
            {
                TraceWarn(AimxLlmInfer, "No required_tools array found in LLM response");
            }

            // Extract other fields
            if (responseJson.contains("analysis_reasoning") && responseJson["analysis_reasoning"].is_string())
            {
                analysisResult.analysisReasoning = Utf8ToWide(responseJson["analysis_reasoning"].get<std::string>());
            }

            if (responseJson.contains("overall_confidence") && responseJson["overall_confidence"].is_number())
            {
                // Convert from 1-100 scale to 0.01-1.0 scale
                int confidenceInt = responseJson["overall_confidence"].get<int>();
                analysisResult.overallConfidence = static_cast<float>(confidenceInt) / 100.0f;
            }

            if (responseJson.contains("requires_user_confirmation") && responseJson["requires_user_confirmation"].is_boolean())
            {
                analysisResult.requiresUserConfirmation = responseJson["requires_user_confirmation"].get<bool>();
            }

            if (responseJson.contains("suggested_approach") && responseJson["suggested_approach"].is_string())
            {
                analysisResult.suggestedApproach = Utf8ToWide(responseJson["suggested_approach"].get<std::string>());
            }

            // Send detailed parsed results to conversation session
            if (conversationSession)
            {
                // Show LLM's understanding and reasoning
                if (!analysisResult.analysisReasoning.empty())
                {
                    std::wstring reasoningMsg = L"[" + std::wstring(AimxConstants::MessageStages::AIMX_STAGE_LLM_PARSING) + L"] LLM Analysis Reasoning: " + analysisResult.analysisReasoning;
                    conversationSession->SendMessage(AIMX_MSG_LLM_RESPONSE, reasoningMsg);
                }

                if (!analysisResult.suggestedApproach.empty())
                {
                    std::wstring approachMsg = L"[" + std::wstring(AimxConstants::MessageStages::AIMX_STAGE_LLM_PARSING) + L"] LLM Suggested Approach: " + analysisResult.suggestedApproach;
                    conversationSession->SendMessage(AIMX_MSG_LLM_RESPONSE, approachMsg);
                }

                // Show tool analysis results
                if (analysisResult.requiredTools.empty())
                {
                    std::wstring noToolsMsg = L"LLM Decision: No specific tools required. Will use direct LLM processing.";
                    if (analysisResult.overallConfidence > 0)
                    {
                        noToolsMsg += L" (Confidence: " + std::to_wstring(analysisResult.overallConfidence) + L")";
                    }
                    conversationSession->SendMessage(AIMX_MSG_TOOL_ANALYSIS, L"[" + std::wstring(AimxConstants::MessageStages::AIMX_STAGE_LLM_PARSING) + L"] " + noToolsMsg);
                }
                else
                {
                    std::wstring toolsFoundMsg = L"[" + std::wstring(AimxConstants::MessageStages::AIMX_STAGE_LLM_PARSING) + L"] LLM Decision: Found " + std::to_wstring(analysisResult.requiredTools.size()) + L" required tools:";
                    conversationSession->SendMessage(AIMX_MSG_TOOL_ANALYSIS, toolsFoundMsg);

                    for (size_t i = 0; i < analysisResult.requiredTools.size(); ++i)
                    {
                        const auto& tool = analysisResult.requiredTools[i];
                        std::wstring displayName = McpToolManager::CreateDisplayName(tool.serverName, tool.toolName);
                        std::wstring toolMsg = L"  Tool " + std::to_wstring(i + 1) + L": " + displayName;

                        if (tool.confidence > 0)
                        {
                            toolMsg += L" (Confidence: " + std::to_wstring(tool.confidence) + L")";
                        }

                        if (!tool.reasoning.empty())
                        {
                            toolMsg += L" - Reasoning: " + tool.reasoning;
                        }

                        if (!tool.parameters.empty())
                        {
                            std::string params = tool.parameters.dump();
                            toolMsg += L" - Parameters: " + Utf8ToWide(params);
                        }

                        conversationSession->SendMessage(AIMX_MSG_TOOL_ANALYSIS, L"[" + std::wstring(AimxConstants::MessageStages::AIMX_STAGE_LLM_PARSING) + L"] " + toolMsg);
                    }

                    if (analysisResult.overallConfidence > 0)
                    {
                        std::wstring confidenceMsg = L"[" + std::wstring(AimxConstants::MessageStages::AIMX_STAGE_LLM_PARSING) + L"] Overall Analysis Confidence: " + std::to_wstring(analysisResult.overallConfidence);
                        conversationSession->SendMessage(AIMX_MSG_TOOL_ANALYSIS, confidenceMsg);
                    }
                }
            }
        }
        else
        {
            // Fallback: treat as plain text response
            TraceWarn(AimxLlmInfer, "Failed to parse LLM response as JSON, treating as plain text");
            TraceVerb(AimxLlmInfer, "Raw response: %s", utf8Response.c_str());

            analysisResult.analysisReasoning = llmResponse;
            analysisResult.overallConfidence = 0.5f;

            if (conversationSession)
            {
                conversationSession->SendMessage(AIMX_MSG_LLM_RESPONSE, L"[" + std::wstring(AimxConstants::MessageStages::AIMX_STAGE_LLM_PARSING) + L"] LLM returned non-JSON response, treating as plain text analysis");
                if (!analysisResult.analysisReasoning.empty())
                {
                    std::wstring fallbackMsg = L"[" + std::wstring(AimxConstants::MessageStages::AIMX_STAGE_LLM_PARSING) + L"] LLM Analysis (Plain Text): " + analysisResult.analysisReasoning;
                    conversationSession->SendMessage(AIMX_MSG_LLM_RESPONSE, fallbackMsg);
                }
            }
        }

        return S_OK;
    }
    catch (...)
    {
        TraceErr(AimxLlmInfer, "Exception parsing tool analysis response");
        return E_FAIL;
    }
}

HRESULT
LLMInfer::ParseResponseGenerationResult(
    _In_ const std::wstring& llmResponse,
    _Out_ LLM_RESPONSE_RESULT& responseResult,
    _In_opt_ std::shared_ptr<ConversationSession> conversationSession
    )
/*++

Routine Description:
    Parse LLM response for response generation into structured result.

Arguments:
    llmResponse - The raw LLM response
    responseResult - Output parsed response result

Return Value:
    S_OK on success, or an error HRESULT on failure.

--*/
{
    try
    {
        // Initialize result
        responseResult = {};

        // Convert response to UTF-8 for JSON parsing
        std::string utf8Response = WideToUtf8(llmResponse);

        // Sanitize utf8Response if it starts with "```json"
        const std::string fence = "```json";
        const std::string fence_end = "```";

        size_t start = utf8Response.find(fence);
        if (start != std::string::npos)
        {
            size_t json_start = utf8Response.find('\n', start);
            size_t end = utf8Response.rfind(fence_end);

            if (json_start != std::string::npos && end != std::string::npos && end > json_start)
            {
                utf8Response = utf8Response.substr(json_start + 1, end - json_start - 1);
            }
        }

        // Try to parse as JSON
        nlohmann::json responseJson;
        if (nlohmann::json::accept(utf8Response))
        {
            responseJson = nlohmann::json::parse(utf8Response);

            // Extract response fields
            if (responseJson.contains("response") && responseJson["response"].is_string())
            {
                responseResult.response = Utf8ToWide(responseJson["response"].get<std::string>());
            }

            if (responseJson.contains("reasoning") && responseJson["reasoning"].is_string())
            {
                responseResult.reasoning = Utf8ToWide(responseJson["reasoning"].get<std::string>());
            }

            if (responseJson.contains("is_complete") && responseJson["is_complete"].is_boolean())
            {
                responseResult.isComplete = responseJson["is_complete"].get<bool>();
            }
            else
            {
                responseResult.isComplete = true; // Default to complete
            }

            // Send detailed parsed response results
            if (conversationSession)
            {
                if (!responseResult.reasoning.empty())
                {
                    std::wstring reasoningMsg = L"[" + std::wstring(AimxConstants::MessageStages::AIMX_STAGE_LLM_PARSING) + L"] LLM Response Reasoning: " + responseResult.reasoning;
                    conversationSession->SendMessage(AIMX_MSG_LLM_RESPONSE, reasoningMsg);
                }

                std::wstring responseMsg = L"Generated final response (" + std::to_wstring(responseResult.response.length()) + L" characters)";
                if (responseResult.isComplete)
                {
                    responseMsg += L" - Response is complete";
                }
                else
                {
                    responseMsg += L" - Response may be partial";
                }
                conversationSession->SendMessage(AIMX_MSG_LLM_RESPONSE, L"[" + std::wstring(AimxConstants::MessageStages::AIMX_STAGE_LLM_PARSING) + L"] " + responseMsg);
            }
        }
        else
        {
            // Fallback: treat as plain text response
            responseResult.response = llmResponse;
            responseResult.isComplete = true;

            if (conversationSession)
            {
                std::wstring fallbackMsg = L"[" + std::wstring(AimxConstants::MessageStages::AIMX_STAGE_LLM_PARSING) + L"] LLM returned plain text response (" + std::to_wstring(responseResult.response.length()) + L" characters)";
                conversationSession->SendMessage(AIMX_MSG_LLM_RESPONSE, fallbackMsg);
            }
        }

        return S_OK;
    }
    catch (...)
    {
        TraceErr(AimxLlmInfer, "Exception parsing response generation result");
        return E_FAIL;
    }
}

HRESULT
LLMInfer::ValidateToolRequirements(
    _In_ const std::vector<TOOL_REQUIREMENT>& requirements,
    _In_ const std::vector<MCP_TOOL_INFO>& availableTools
    )
/*++

Routine Description:
    Validate that tool requirements can be satisfied by available tools.

Arguments:
    requirements - List of tool requirements from LLM analysis
    availableTools - List of available MCP tools

Return Value:
    S_OK if all requirements can be satisfied, or an error HRESULT on failure.

--*/
{
    try
    {
        for (const auto& requirement : requirements)
        {
            bool toolFound = false;

            for (const auto& tool : availableTools)
            {
                if (tool.toolName == requirement.toolName)
                {
                    toolFound = true;
                    break;
                }
            }

            if (!toolFound)
            {
                TraceWarn(AimxLlmInfer, "Required tool not found: %ws", requirement.toolName.c_str());
                return E_FAIL;
            }
        }

        return S_OK;
    }
    catch (...)
    {
        TraceErr(AimxLlmInfer, "Exception validating tool requirements");
        return E_FAIL;
    }
}

HRESULT
LLMInfer::ExtractLlmContentFromHttpResponse(
    _In_ const std::wstring& httpResponseBody,
    _Out_ std::wstring& llmContent
    )
/*++

Routine Description:
    Extract the actual LLM content from the HTTP response JSON structure.
    The HTTP response contains nested JSON where the actual LLM response
    is in choices[0].message.content field.

Arguments:
    httpResponseBody - The raw HTTP response body as JSON string
    llmContent - Output parameter that will receive the extracted LLM content

Return Value:
    S_OK on success, or an error HRESULT on failure.

--*/
{
    try
    {
        TraceVerb(AimxLlmInfer, "Parsing HTTP response JSON to extract LLM content");

        std::string utf8Response = WideToUtf8(httpResponseBody);
        nlohmann::json httpResponseJson = nlohmann::json::parse(utf8Response);

        // Extract content from choices[0].message.content
        if (httpResponseJson.contains(AimxConstants::JsonFields::AIMX_JSON_KEY_CHOICES) && httpResponseJson[AimxConstants::JsonFields::AIMX_JSON_KEY_CHOICES].is_array() &&
            !httpResponseJson[AimxConstants::JsonFields::AIMX_JSON_KEY_CHOICES].empty())
        {
            auto& firstChoice = httpResponseJson[AimxConstants::JsonFields::AIMX_JSON_KEY_CHOICES][0];

            if (firstChoice.contains(AimxConstants::JsonFields::AIMX_MESSAGE) && firstChoice[AimxConstants::JsonFields::AIMX_MESSAGE].contains(AimxConstants::JsonFields::AIMX_JSON_KEY_CONTENT))
            {
                std::string content = firstChoice[AimxConstants::JsonFields::AIMX_MESSAGE][AimxConstants::JsonFields::AIMX_JSON_KEY_CONTENT].get<std::string>();
                llmContent = Utf8ToWide(content);

                TraceInfo(AimxLlmInfer, "Successfully extracted LLM content length: %d",
                         static_cast<int>(llmContent.length()));
                TraceVerb(AimxLlmInfer, "Extracted LLM content: %s", content.c_str());

                return S_OK;
            }
            else
            {
                TraceErr(AimxLlmInfer, "Invalid response structure: missing message.content");
                LOGERROR("Invalid response structure: missing message.content");
                return E_FAIL;
            }
        }
        else
        {
            TraceErr(AimxLlmInfer, "Invalid response structure: missing choices array");
            LOGERROR("Invalid response structure: missing choices array");
            return E_FAIL;
        }
    }
    catch (const nlohmann::json::exception& e)
    {
        TraceErr(AimxLlmInfer, "Failed to parse HTTP response JSON: %s", e.what());
        return E_FAIL;
    }
    catch (...)
    {
        TraceErr(AimxLlmInfer, "Unknown exception in ExtractLlmContentFromHttpResponse");
        return E_FAIL;
    }
}


